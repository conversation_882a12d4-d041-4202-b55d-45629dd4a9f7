{% extends "base.html" %}

{% block title %}产品咨询管理系统{% endblock %}

{% block extra_css %}
<link href="{{ url_for('static', filename='css/inquiry.css') }}" rel="stylesheet">
<style>
    /* 整体布局调整 */
    .card-body {
        padding: 1rem;
    }
    
    /* 产品卡片样式 */
    .product-item {
        border: none;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 1.5rem;
        border-radius: 8px;
        border-left: 4px solid #4a90e2;
        background-color: #f8f9fa;
    }
    
    .product-item .card-body {
        padding: 1rem;
    }
    
    /* 表单组样式调整 */
    .mb-3 {
        margin-bottom: 0.8rem !important;
    }
    
    /* 供应商报价区域样式 */
    .supplier-quotes {
        background-color: #fff;
        padding: 1rem;
        border-radius: 6px;
        margin-top: 1rem;
    }
    
    .supplier-quotes h6 {
        color: #2c3e50;
        font-size: 0.95rem;
        margin-bottom: 0.8rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid #e9ecef;
    }
    
    .supplier-quote {
        background-color: #f8f9fa;
        padding: 0.8rem;
        border-radius: 6px;
        margin-bottom: 0.8rem;
        border-left: 3px solid #28a745;
    }
    
    /* 输入框样式优化 */
    .form-control {
        padding: 0.4rem 0.8rem;
        font-size: 0.9rem;
    }
    
    .input-group-text {
        padding: 0.4rem 0.8rem;
        font-size: 0.9rem;
    }
    
    /* 按钮样式优化 */
    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }
    
    /* 标签样式 */
    .form-label {
        font-size: 0.9rem;
        margin-bottom: 0.3rem;
        color: #495057;
    }
    
    /* 交货周期按钮组 */
    .btn-group .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }
    
    /* 产品之间的分隔 */
    .product-item + .product-item {
        margin-top: 1.5rem;
    }
    
    /* 供应商报价之间的分隔 */
    .supplier-quote + .supplier-quote {
        margin-top: 0.8rem;
    }
    
    /* 必填字段标记 */
    .required::after {
        content: " *";
        color: #dc3545;
    }
    
    /* 交替产品颜色 */
    .product-item:nth-child(odd) {
        border-left-color: #4a90e2;
    }
    
    .product-item:nth-child(even) {
        border-left-color: #6c5ce7;
    }
    
    /* 交替供应商报价颜色 */
    .supplier-quote:nth-child(odd) {
        border-left-color: #28a745;
    }
    
    .supplier-quote:nth-child(even) {
        border-left-color: #fd7e14;
    }
    
    /* 悬浮效果 */
    .product-item:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }
    
    .supplier-quote:hover {
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>产品咨询管理系统</h2>
    <a href="{{ url_for('index') }}" class="btn btn-outline-secondary">返回首页</a>
</div>

<!-- 产品搜索表单 -->
<div class="card mb-4">
    <div class="card-body">
        <h5 class="card-title">产品搜索</h5>
        <div class="search-container">
            <div class="input-group">
                <input type="text" id="searchProduct" class="form-control" placeholder="输入产品名称搜索...">
                <button class="btn btn-outline-secondary" type="button" onclick="searchProducts()">搜索</button>
            </div>
            <div id="searchResults" class="list-group">
                <!-- 搜索结果将在这里动态显示 -->
            </div>
        </div>
    </div>
</div>

<!-- 咨询表单 -->
<div class="card">
    <div class="card-body">
        <h5 class="card-title">新增产品咨询</h5>
        <form id="inquiryForm">
            <!-- 客户信息 -->
            <div class="mb-3">
                <label for="customer" class="form-label required">客户</label>
                <div class="input-group">
                    <select class="form-select" id="customer" name="customer" required>
                        <option value="">选择客户...</option>
                        <!-- 客户选项将通过 JavaScript 动态加载 -->
                    </select>
                    <button class="btn btn-outline-secondary" type="button" onclick="showNewCustomerModal()">
                        新增客户
                    </button>
                </div>
            </div>

            <!-- 添加批量输入按钮 -->
            <button type="button" class="btn btn-outline-primary mb-3" onclick="showBulkProductModal()">
                <i class="bi bi-boxes"></i> 批量添加供应商产品
            </button>

            <!-- 产品列表 -->
            <div id="productList">
                <div class="product-item card mb-3">
                    <div class="card-body">
                        <!-- 产品信息 -->
                        <div class="mb-3">
                            <label for="brand" class="form-label">品牌</label>
                            <input type="text" class="form-control product-brand" name="brand">
                        </div>
                        
                        <div class="mb-3">
                            <label for="productName" class="form-label required">产品名称</label>
                            <input type="text" class="form-control product-name" name="productName" required>
                        </div>

                        <!-- 产品数量 -->
                        <div class="mb-3">
                            <label for="quantity" class="form-label required">数量</label>
                            <input type="number" class="form-control product-quantity" name="quantity" min="1" required>
                        </div>

                        <!-- 交货周期 -->
                        <div class="mb-3">
                            <label class="form-label required">预计交货周期</label>
                            <div class="row g-3">
                                <div class="col-md-4">
                                    <div class="input-group">
                                        <input type="number" class="form-control delivery-days" min="0" value="0" onchange="updateExpectedDeliveryDays(this)">
                                        <span class="input-group-text">天</span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="input-group">
                                        <input type="number" class="form-control delivery-weeks" min="0" value="0" onchange="updateExpectedDeliveryDays(this)">
                                        <span class="input-group-text">周</span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="input-group">
                                        <input type="number" class="form-control delivery-months" min="0" value="0" onchange="updateExpectedDeliveryDays(this)">
                                        <span class="input-group-text">月</span>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-2">
                                <button type="button" class="btn btn-outline-secondary btn-sm me-2" onclick="setDeliveryPeriod(this, 28)">4周</button>
                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="setDeliveryPeriod(this, 15)">15天</button>
                            </div>
                            <input type="hidden" class="expected-delivery-days" name="expectedDeliveryDays" required>
                            <div class="form-text mt-2">总计: <span class="total-days">0</span> 天</div>
                        </div>

                        <!-- 供应商报价 -->
                        <div class="supplier-quotes">
                            <h6 class="mb-3">供应商报价</h6>
                            <div class="supplier-quote">
                                <div class="row g-3 mb-3">
                                    <div class="col-md-4">
                                        <label class="form-label">供应商名称</label>
                                        <input type="text" class="form-control supplier-name" placeholder="输入供应商名称" required>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">联系方式</label>
                                        <input type="text" class="form-control supplier-contact" placeholder="输入联系方式">
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">报价</label>
                                        <div class="input-group">
                                            <span class="input-group-text">¥</span>
                                            <input type="number" class="form-control supplier-price" step="0.01" min="0" required>
                                        </div>
                                    </div>
                                </div>
                                <div class="row align-items-end">
                                    <div class="col-md-10">
                                        <label class="form-label">供应商交货周期</label>
                                        <div class="row g-2">
                                            <div class="col-md-3">
                                                <div class="input-group">
                                                    <input type="number" class="form-control supplier-delivery-days" min="0" value="0" onchange="updateSupplierDeliveryDays(this)">
                                                    <span class="input-group-text">天</span>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="input-group">
                                                    <input type="number" class="form-control supplier-delivery-weeks" min="0" value="0" onchange="updateSupplierDeliveryDays(this)">
                                                    <span class="input-group-text">周</span>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="input-group">
                                                    <input type="number" class="form-control supplier-delivery-months" min="0" value="0" onchange="updateSupplierDeliveryDays(this)">
                                                    <span class="input-group-text">月</span>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-text mb-2">总计: <span class="supplier-total-days">0</span> 天</div>
                                            </div>
                                        </div>
                                        <input type="hidden" class="supplier-expected-delivery-days" name="supplierExpectedDeliveryDays" value="0">
                                    </div>
                                    <div class="col-md-2">
                                        <div class="btn-group w-100">
                                            <button type="button" class="btn btn-outline-primary" onclick="saveSupplier(this)" title="保存供应商">
                                                <i class="bi bi-save"></i> 保存
                                            </button>
                                            <button type="button" class="btn btn-outline-danger" onclick="removeSupplierQuote(this)">
                                                <i class="bi bi-trash"></i> 删除
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-3 mb-3">
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="addSupplierQuote(this)">
                                <i class="bi bi-plus"></i> 添加供应商报价
                            </button>
                        </div>

                        <!-- 报价信息 -->
                        {% if is_admin %}
                        <div class="mb-3">
                            <label for="myPrice" class="form-label required">我的报价</label>
                            <div class="input-group">
                                <span class="input-group-text">¥</span>
                                <input type="number" class="form-control product-price" name="myPrice" step="0.01" min="0" required>
                            </div>
                        </div>
                        {% endif %}

                        <button type="button" class="btn btn-outline-danger btn-sm float-end" onclick="removeProduct(this)">
                            删除产品
                        </button>
                    </div>
                </div>
            </div>

            <button type="button" class="btn btn-outline-primary mb-3" onclick="addProduct()">
                添加产品
            </button>

            <!-- 询价日期 -->
            <div class="mb-3">
                <label for="inquiryDate" class="form-label required">询价日期</label>
                <input type="date" class="form-control" id="inquiryDate" name="inquiryDate" required>
            </div>
            {% if is_admin %}
            <div class="d-grid gap-2">
                <button type="submit" class="btn btn-primary">提交咨询</button>
            </div>
            {% endif %}
        </form>
    </div>
</div>

<!-- 批量添加供应商产品模态框 - 移到表单外部 -->
<div class="modal fade" id="bulkProductModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">批量添加供应商产品</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="bulkProductForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">供应商信息</label>
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="input-group">
                                    <input type="text" class="form-control" id="bulkSupplierName" name="supplierName" placeholder="供应商名称" required>
                                    <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                        选择供应商
                                    </button>
                                    <ul class="dropdown-menu dropdown-menu-end" id="bulkSupplierList">
                                        <!-- 供应商列表将通过JavaScript动态填充 -->
                                    </ul>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <input type="text" class="form-control" id="bulkSupplierContact" name="supplierContact" placeholder="联系方式">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">产品列表</label>
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover" id="bulkProductTable">
                                <thead class="table-light">
                                    <tr>
                                        <th style="width: 15%">品牌</th>
                                        <th style="width: 35%">产品名称</th>
                                        <th style="width: 10%">数量</th>
                                        <th style="width: 20%">报价</th>
                                        <th style="width: 15%">交货天数</th>
                                        <th style="width: 5%">操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><input type="text" class="form-control form-control-sm bulk-brand" name="brand[]"></td>
                                        <td><input type="text" class="form-control form-control-sm bulk-product-name" name="productName[]" required style="font-size: 1.1em"></td>
                                        <td><input type="number" class="form-control form-control-sm bulk-quantity" name="quantity[]" min="1" required style="width: 80px"></td>
                                        <td>
                                            <div class="input-group input-group-sm">
                                                <span class="input-group-text">¥</span>
                                                <input type="number" class="form-control bulk-price" name="price[]" step="0.01" min="0" required>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="input-group input-group-sm">
                                                <input type="number" class="form-control bulk-delivery-days" name="deliveryDays[]" min="0" required>
                                                <span class="input-group-text">天</span>
                                            </div>
                                        </td>
                                        <td>
                                            <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeBulkRow(this)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="addBulkRow()">
                                <i class="bi bi-plus"></i> 添加行
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="loadSupplierProducts()">
                                <i class="bi bi-arrow-clockwise"></i> 加载供应商历史产品
                            </button>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 新增客户模态框 -->
<div class="modal fade" id="newCustomerModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">新增客户</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="newCustomerName" class="form-label">客户名称 <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="newCustomerName" required>
                </div>
                <div class="mb-3">
                    <label for="newCustomerAddress" class="form-label">地址 <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="newCustomerAddress" required>
                </div>
                <div class="mb-3">
                    <label for="newCustomerPhone" class="form-label">电话 <span class="text-danger">*</span></label>
                    <input type="tel" class="form-control" id="newCustomerPhone" required>
                </div>
                <div class="mb-3">
                    <label for="newCustomerContact" class="form-label">联系人 <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="newCustomerContact" required>
                </div>
                <div class="mb-3">
                    <label for="newCustomerCountry" class="form-label">国家 <span class="text-danger">*</span></label>
                    <select class="form-select" id="newCustomerCountry" required>
                        <option value="">选择国家...</option>
                        <!-- 国家选项将通过JavaScript动态填充 -->
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="createCustomer()">保存</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/inquiry.js') }}"></script>
{% endblock %} 