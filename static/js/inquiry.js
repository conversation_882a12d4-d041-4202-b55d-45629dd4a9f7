// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('Page loaded, initializing...');  // Debug log
    
    // 加载客户列表
    loadCustomers();
    // 加载国家列表
    loadCountries();
    // 设置默认日期为今天
    setDefaultDate();
    // 初始调整搜索结果宽度
    adjustSearchResultsWidth();
    // 更新供应商报价行模板
    updateSupplierQuoteTemplate();
    // 添加产品名称输入事件监听
    setupProductNameListener();
    
    // 初始化所有交货周期输入框
    initializeDeliveryDays();
    
    // 绑定表单提交事件
    const inquiryForm = document.getElementById('inquiryForm');
    console.log('Found inquiry form:', inquiryForm);  // Debug log
    
    if (inquiryForm) {
        console.log('Setting up inquiry form event listeners');  // Debug log
        // 移除可能存在的旧事件监听器
        const newForm = inquiryForm.cloneNode(true);
        inquiryForm.parentNode.replaceChild(newForm, inquiryForm);
        
        // 添加新的事件监听器
        newForm.addEventListener('submit', function(event) {
            console.log('Inquiry form submit event triggered');  // Debug log
            event.preventDefault();
            submitInquiry(event);
        });
        
        // 添加表单验证
        newForm.addEventListener('input', function() {
            const submitBtn = this.querySelector('[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = !this.checkValidity();
                console.log('Submit button state updated:', !this.checkValidity());  // Debug log
            }
        });
    } else {
        console.error('Inquiry form not found');  // Debug log
    }
    
    // 绑定批量添加表单事件
    const bulkProductForm = document.getElementById('bulkProductForm');
    console.log('Found bulk product form:', bulkProductForm);  // Debug log
    
    if (bulkProductForm) {
        console.log('Setting up bulk product form event listeners');  // Debug log
        // 如果已经设置过监听器，就跳过
        if (bulkProductForm.dataset.hasListener) {
            return;
        }
        
        // 添加事件监听器
        bulkProductForm.addEventListener('submit', function(e) {
            console.log('Bulk product form submit event triggered');
            e.preventDefault();
            saveBulkProducts(e);
        });
        
        // 标记已设置监听器
        bulkProductForm.dataset.hasListener = 'true';
    } else {
        console.error('Bulk product form not found');  // Debug log
    }
});

// 初始化所有交货周期输入框
function initializeDeliveryDays() {
    // 初始化产品交货周期
    document.querySelectorAll('.product-item').forEach(productItem => {
        const months = parseInt(productItem.querySelector('.delivery-months')?.value) || 0;
        const weeks = parseInt(productItem.querySelector('.delivery-weeks')?.value) || 0;
        const days = parseInt(productItem.querySelector('.delivery-days')?.value) || 0;
        
        const totalDays = months * 30 + weeks * 7 + days;
        productItem.querySelector('.expected-delivery-days').value = totalDays;
        productItem.querySelector('.total-days').textContent = totalDays;
    });
    
    // 初始化供应商交货周期
    document.querySelectorAll('.supplier-quote').forEach(quote => {
        const months = parseInt(quote.querySelector('.supplier-delivery-months')?.value) || 0;
        const weeks = parseInt(quote.querySelector('.supplier-delivery-weeks')?.value) || 0;
        const days = parseInt(quote.querySelector('.supplier-delivery-days')?.value) || 0;
        
        const totalDays = months * 30 + weeks * 7 + days;
        quote.querySelector('.supplier-expected-delivery-days').value = totalDays;
        quote.querySelector('.supplier-total-days').textContent = totalDays;
    });
}

// 更新交货周期总天数
function updateExpectedDeliveryDays(input) {
    if (!input) return;
    
    // 找到最近的交货周期输入组
    const container = input.closest('.mb-3');
    if (!container) return;
    
    // 获取月、周、天的值
    const months = parseInt(container.querySelector('.delivery-months')?.value) || 0;
    const weeks = parseInt(container.querySelector('.delivery-weeks')?.value) || 0;
    const days = parseInt(container.querySelector('.delivery-days')?.value) || 0;
    
    // 计算总天数
    const totalDays = months * 30 + weeks * 7 + days;
    
    // 更新隐藏输入和显示
    const expectedDeliveryDays = container.querySelector('.expected-delivery-days');
    const totalDaysSpan = container.querySelector('.total-days');
    
    if (expectedDeliveryDays) expectedDeliveryDays.value = totalDays;
    if (totalDaysSpan) totalDaysSpan.textContent = totalDays;
}

// 设置默认日期为今天
function setDefaultDate() {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    document.getElementById('inquiryDate').value = `${year}-${month}-${day}`;
}

// 加载客户列表
async function loadCustomers() {
    try {
        const response = await fetch('/customers');
        const customers = await response.json();
        
        const select = document.getElementById('customer');
        // 保留第一个选项
        select.innerHTML = '<option value="">选择客户...</option>';
        
        // 添加客户选项
        customers.forEach(customer => {
            const option = document.createElement('option');
            option.value = customer.id;
            option.textContent = customer.name;
            select.appendChild(option);
        });
    } catch (error) {
        console.error('加载客户列表失败:', error);
    }
}

// 显示新增客户模态框
function showNewCustomerModal() {
    const modal = new bootstrap.Modal(document.getElementById('newCustomerModal'));
    document.getElementById('newCustomerName').value = '';
    modal.show();
}

// 加载国家列表
async function loadCountries() {
    try {
        const response = await fetch('/customer/countries');
        const countries = await response.json();
        
        const select = document.getElementById('newCustomerCountry');
        select.innerHTML = '<option value="">选择国家...</option>';
        
        countries.forEach(country => {
            const option = document.createElement('option');
            option.value = country.code;
            option.textContent = country.name;
            select.appendChild(option);
        });
    } catch (error) {
        console.error('加载国家列表失败:', error);
    }
}

// 创建新客户
async function createCustomer() {
    const nameInput = document.getElementById('newCustomerName');
    const addressInput = document.getElementById('newCustomerAddress');
    const phoneInput = document.getElementById('newCustomerPhone');
    const contactInput = document.getElementById('newCustomerContact');
    const countryInput = document.getElementById('newCustomerCountry');
    
    const name = nameInput.value.trim();
    const address = addressInput.value.trim();
    const phone = phoneInput.value.trim();
    const contact = contactInput.value.trim();
    const country = countryInput.value;
    
    if (!name) {
        alert('请输入客户名称');
        return;
    }
    
    if (!address) {
        alert('请输入地址');
        return;
    }
    
    if (!phone) {
        alert('请输入电话');
        return;
    }
    
    if (!contact) {
        alert('请输入联系人');
        return;
    }
    
    if (!country) {
        alert('请选择国家');
        return;
    }
    
    try {
        const response = await fetch('/customer/add', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                name: name,
                address: address,
                phone: phone,
                contact: contact,
                country: country
            })
        });
        
        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.error || '创建客户失败');
        }
        
        const customer = await response.json();
        
        // 添加到选择框并选中
        const select = document.getElementById('customer');
        const option = document.createElement('option');
        option.value = customer.id;
        option.textContent = customer.name;
        select.appendChild(option);
        select.value = customer.id;
        
        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('newCustomerModal'));
        modal.hide();
        
        // 清空表单
        nameInput.value = '';
        addressInput.value = '';
        phoneInput.value = '';
        contactInput.value = '';
        countryInput.value = '';
        
    } catch (error) {
        console.error('创建客户失败:', error);
        alert(error.message || '创建客户失败，请重试');
    }
}

// 更新供应商交货周期总天数
function updateSupplierDeliveryDays(input) {
    // 找到最近的供应商报价容器
    const container = input.closest('.supplier-quote');
    
    // 获取月、周、天的值
    const months = parseInt(container.querySelector('.supplier-delivery-months').value) || 0;
    const weeks = parseInt(container.querySelector('.supplier-delivery-weeks').value) || 0;
    const days = parseInt(container.querySelector('.supplier-delivery-days').value) || 0;
    
    // 计算总天数
    const totalDays = months * 30 + weeks * 7 + days;
    
    // 更新隐藏输入和显示
    container.querySelector('.supplier-expected-delivery-days').value = totalDays;
    container.querySelector('.supplier-total-days').textContent = totalDays;
}

// 保存供应商信息
async function saveSupplier(button) {
    console.log('saveSupplier called');  // Debug log
    
    // 检查客户是否已选择
    const customerId = document.getElementById('customer').value;
    if (!customerId) {
        console.log('No customer selected');  // Debug log
        showToast('warning', '请先选择客户');
        return;
    }
    
    const quoteElement = button.closest('.supplier-quote');
    const productItem = button.closest('.product-item');
    
    const supplierName = quoteElement.querySelector('.supplier-name').value.trim();
    const supplierContact = quoteElement.querySelector('.supplier-contact').value.trim();
    const supplierPrice = parseFloat(quoteElement.querySelector('.supplier-price').value) || 0;
    const deliveryDays = parseInt(quoteElement.querySelector('.supplier-expected-delivery-days').value) || 0;
    
    const productName = productItem.querySelector('.product-name').value.trim();
    const brand = productItem.querySelector('.product-brand').value.trim();
    const quantity = parseInt(productItem.querySelector('.product-quantity').value) || 0;
    
    // 只在价格输入框存在时获取价格
    const priceInput = productItem.querySelector('.product-price');
    const myPrice = priceInput ? (parseFloat(priceInput.value) || 0) : 0;
    
    if (!supplierName) {
        showToast('warning', '请输入供应商名称');
        return;
    }
    
    if (!productName) {
        showToast('warning', '请先输入产品名称');
        return;
    }
    
    if (!supplierPrice) {
        showToast('warning', '请输入供应商报价');
        return;
    }

    try {
        const response = await fetch('/suppliers', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                name: supplierName,
                contact: supplierContact,
                default_delivery_days: deliveryDays,
                product_name: productName,
                brand: brand,
                quantity: quantity,
                my_price: myPrice,
                price: supplierPrice,
                delivery_days: deliveryDays,
                inquiry_date: document.getElementById('inquiryDate').value,
                customer_id: customerId  // 添加客户ID
            })
        });
        
        if (!response.ok) {
            throw new Error('保存供应商失败');
        }
        
        const result = await response.json();
        
        // 如果有历史报价信息，显示它们
        if (result.historical_quotes && result.historical_quotes.length > 0) {
            showSupplierHistory(result.historical_quotes);
        }
        
        showToast('success', '供应商信息已保存');
        
        // 更新当前供应商报价行的状态
        quoteElement.classList.add('saved');
        const saveButton = quoteElement.querySelector('button[onclick*="saveSupplier"]');
        if (saveButton) {
            saveButton.innerHTML = '<i class="bi bi-check2"></i> 已存';
            saveButton.classList.remove('btn-outline-primary');
            saveButton.classList.add('btn-success');
            saveButton.style.minWidth = '65px';  // 设置最小宽度
        }
        
    } catch (error) {
        console.error('保存供应商失败:', error);
        showToast('error', error.message || '保存供应商失败，请重试');
    }
}

// 显示供应商历史报价信息
function showSupplierHistory(quotes) {
    // 创建模态框内容
    let modalContent = `
        <div class="modal fade" id="supplierHistoryModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">供应商历史报价</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>询价日期</th>
                                        <th>产品名称</th>
                                        <th>品牌</th>
                                        <th>数量</th>
                                        <th>报价</th>
                                        <th>交货周期</th>
                                    </tr>
                                </thead>
                                <tbody>
    `;
    
    quotes.forEach(quote => {
        modalContent += `
            <tr>
                <td>${new Date(quote.inquiry_date).toLocaleDateString()}</td>
                <td>${quote.product_name}</td>
                <td>${quote.brand || '-'}</td>
                <td>${quote.quantity}</td>
                <td>¥${quote.price.toFixed(2)}</td>
                <td>${formatDeliveryPeriod(quote.delivery_days)}</td>
            </tr>
        `;
    });
    
    modalContent += `
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // 移除可能存在的旧模态框
    const oldModal = document.getElementById('supplierHistoryModal');
    if (oldModal) {
        oldModal.remove();
    }
    
    // 添加新模态框到页面
    document.body.insertAdjacentHTML('beforeend', modalContent);
    
    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('supplierHistoryModal'));
    modal.show();
}

// 当选择供应商时获取历史信息
async function onSupplierSelect(input) {
    const datalistId = input.getAttribute('list');
    const datalist = document.getElementById(datalistId);
    const options = datalist.getElementsByTagName('option');
    const productItem = input.closest('.product-item');
    const productName = productItem.querySelector('.product-name').value.trim();
    
    for (let option of options) {
        if (option.value === input.value) {
            const quoteElement = input.closest('.supplier-quote');
            quoteElement.querySelector('.supplier-contact').value = option.dataset.contact || '';
            
            const deliveryDays = parseInt(option.dataset.deliveryDays) || 0;
            const months = Math.floor(deliveryDays / 30);
            const remainingDays = deliveryDays % 30;
            const weeks = Math.floor(remainingDays / 7);
            const days = remainingDays % 7;
            
            quoteElement.querySelector('.supplier-delivery-months').value = months;
            quoteElement.querySelector('.supplier-delivery-weeks').value = weeks;
            quoteElement.querySelector('.supplier-delivery-days').value = days;
            quoteElement.querySelector('.supplier-expected-delivery-days').value = deliveryDays;
            quoteElement.querySelector('.supplier-total-days').textContent = deliveryDays;
            
            // 如果有产品名称，获取历史报价信息
            if (productName) {
                try {
                    const response = await fetch(`/suppliers/history?supplier_name=${encodeURIComponent(input.value)}&product_name=${encodeURIComponent(productName)}`);
                    if (response.ok) {
                        const result = await response.json();
                        if (result.historical_quotes && result.historical_quotes.length > 0) {
                            showSupplierHistory(result.historical_quotes);
                        }
                    }
                } catch (error) {
                    console.error('获取供应商历史信息失败:', error);
                }
            }
            
            break;
        }
    }
}

// 加载保存的供应商列表
async function loadSavedSuppliers() {
    try {
        const response = await fetch('/suppliers');
        if (!response.ok) {
            throw new Error('获取供应商列表失败');
        }
        return await response.json();
    } catch (error) {
        console.error('获取供应商列表失败:', error);
        showToast('error', '获取供应商列表失败');
        return [];
    }
}

// 加载供应商建议列表
async function loadSupplierSuggestions(input) {
    try {
        const response = await fetch(`/suppliers/search?q=${encodeURIComponent(input.value)}`);
        if (!response.ok) {
            throw new Error('获取供应商建议失败');
        }
        const suppliers = await response.json();
        
        // 更新datalist
        const datalistId = input.getAttribute('list');
        const datalist = document.getElementById(datalistId);
        datalist.innerHTML = '';
        
        suppliers.forEach(supplier => {
            const option = document.createElement('option');
            option.value = supplier.name;
            option.dataset.contact = supplier.contact;
            option.dataset.deliveryDays = supplier.default_delivery_days;
            datalist.appendChild(option);
        });
    } catch (error) {
        console.error('获取供应商建议失败:', error);
    }
}

// 更新供应商报价行模板
function updateSupplierQuoteTemplate() {
    const supplierQuotes = document.querySelectorAll('.supplier-quote');
    supplierQuotes.forEach(quote => {
        // 检查是否已经有按钮组
        if (!quote.querySelector('.btn-group')) {
            const buttonContainer = quote.querySelector('.col-md-2');
            const deleteButton = buttonContainer.querySelector('button');
            
            // 创建按钮组
            const btnGroup = document.createElement('div');
            btnGroup.className = 'btn-group';
            
            // 创建保存按钮
            const saveButton = document.createElement('button');
            saveButton.type = 'button';
            saveButton.className = 'btn btn-outline-primary';
            saveButton.innerHTML = '<i class="bi bi-save"></i> 保存';
            saveButton.style.minWidth = '65px';
            saveButton.onclick = function() { saveSupplier(this); };
            
            // 更新删除按钮样式
            deleteButton.className = 'btn btn-outline-danger';
            deleteButton.innerHTML = '<i class="bi bi-trash"></i> 删除';
            deleteButton.style.minWidth = '65px';
            
            // 组装按钮组
            btnGroup.appendChild(saveButton);
            btnGroup.appendChild(deleteButton);
            
            // 替换原有的删除按钮
            buttonContainer.innerHTML = '';
            buttonContainer.appendChild(btnGroup);
        }
        
        // 添加供应商名称自动完成功能
        const nameInput = quote.querySelector('.supplier-name');
        if (!nameInput.hasAttribute('list')) {
            const datalistId = 'supplierList' + Math.random().toString(36).substr(2, 9);
            const datalist = document.createElement('datalist');
            datalist.id = datalistId;
            nameInput.setAttribute('list', datalistId);
            nameInput.parentNode.appendChild(datalist);
            
            // 添加事件监听器
            nameInput.addEventListener('input', () => loadSupplierSuggestions(nameInput));
            nameInput.addEventListener('change', () => onSupplierSelect(nameInput));
        }
    });
}

// 添加供应商报价行
function addSupplierQuote(button) {
    // 找到最近的供应商报价容器
    const container = button.closest('.card-body').querySelector('.supplier-quotes');
    const template = container.querySelector('.supplier-quote').cloneNode(true);
    
    // 清空新行的输入值
    template.querySelectorAll('input').forEach(input => {
        input.value = '';
    });
    
    // 重置交货周期
    template.querySelectorAll('.supplier-delivery-months, .supplier-delivery-weeks, .supplier-delivery-days').forEach(input => {
        input.value = '0';
    });
    template.querySelector('.supplier-total-days').textContent = '0';
    template.querySelector('.supplier-expected-delivery-days').value = '0';
    
    // 重置保存按钮状态
    const saveButton = template.querySelector('button[onclick*="saveSupplier"]');
    if (saveButton) {
        saveButton.innerHTML = '<i class="bi bi-save"></i> 保存';
        saveButton.classList.remove('btn-success');
        saveButton.classList.add('btn-outline-primary');
        saveButton.style.minWidth = '65px';
    }
    
    // 移除saved类
    template.classList.remove('saved');
    
    container.appendChild(template);
    updateSupplierQuoteTemplate();
}

// 删除供应商报价行
function removeSupplierQuote(button) {
    const quoteRows = document.querySelectorAll('.supplier-quote');
    if (quoteRows.length > 1) {
        button.closest('.supplier-quote').remove();
    } else {
        alert('至少需要保留一个供应商报价');
    }
}

// 产品搜索功能
let searchTimeout;
let lastError = null;
let lastErrorTime = 0;

// 调整搜索结果宽度
function adjustSearchResultsWidth() {
    const searchInput = document.querySelector('#searchProduct');
    const searchResults = document.querySelector('#searchResults');
    if (searchInput && searchResults) {
        const inputRect = searchInput.getBoundingClientRect();
        searchResults.style.width = inputRect.width + 'px';
    }
}

// 监听窗口大小变化
window.addEventListener('resize', adjustSearchResultsWidth);

// 设置预定义的交货周期
function setDeliveryPeriod(button, days) {
    // 找到最近的交货周期输入组
    const container = button.closest('.mb-3');
    
    // 计算月、周、天
    const months = Math.floor(days / 30);
    const remainingDays = days % 30;
    const weeks = Math.floor(remainingDays / 7);
    const finalDays = remainingDays % 7;
    
    // 设置输入值（按天、周、月的顺序）
    container.querySelector('.delivery-days').value = finalDays;
    container.querySelector('.delivery-weeks').value = weeks;
    container.querySelector('.delivery-months').value = months;
    
    // 更新总天数
    const totalDays = months * 30 + weeks * 7 + finalDays;
    container.querySelector('.expected-delivery-days').value = totalDays;
    container.querySelector('.total-days').textContent = totalDays;
}

// 将天数转换为友好显示格式
function formatDeliveryPeriod(days) {
    const months = Math.floor(days / 30);
    const remainingDays = days % 30;
    const weeks = Math.floor(remainingDays / 7);
    const finalDays = remainingDays % 7;
    
    let parts = [];
    if (finalDays > 0) parts.push(`${finalDays}天`);
    if (weeks > 0) parts.push(`${weeks}周`);
    if (months > 0) parts.push(`${months}月`);
    
    return parts.length > 0 ? parts.join('') : '0天';
}

// 填充产品和供应商信息
function fillProductInfo(product) {
    // 找到第一个产品项
    const productItem = document.querySelector('.product-item');
    
    // 添加product_id用于标识产品
    productItem.dataset.productId = `${product.brand || ''}-${product.product_name}`.toLowerCase().replace(' ', '-');
    
    // 填充产品信息
    productItem.querySelector('.product-brand').value = product.brand || '';
    productItem.querySelector('.product-name').value = product.product_name;
    productItem.querySelector('.product-quantity').value = product.quantity;
    productItem.querySelector('.product-price').value = product.my_price;
    
    // 设置交货周期
    const days = product.expected_delivery_days;
    const months = Math.floor(days / 30);
    const remainingDays = days % 30;
    const weeks = Math.floor(remainingDays / 7);
    const finalDays = remainingDays % 7;
    
    productItem.querySelector('.delivery-days').value = finalDays;
    productItem.querySelector('.delivery-weeks').value = weeks;
    productItem.querySelector('.delivery-months').value = months;
    
    // 更新总天数显示
    productItem.querySelector('.expected-delivery-days').value = days;
    productItem.querySelector('.total-days').textContent = days;
    
    // 设置询价日期
    if (product.inquiry_date) {
        document.getElementById('inquiryDate').value = product.inquiry_date.split('T')[0];
    }
    
    // 清空现有供应商报价
    const supplierQuotes = productItem.querySelector('.supplier-quotes');
    const supplierQuotesTitle = supplierQuotes.querySelector('h6');
    supplierQuotes.innerHTML = '';
    supplierQuotes.appendChild(supplierQuotesTitle);
    
    // 添加历史供应商报价
    product.supplier_quotes.forEach((quote, index) => {
        const quoteElement = document.createElement('div');
        quoteElement.className = 'supplier-quote mb-3';
        
        // 计算供应商交货周期
        const supplierDays = quote.delivery_days || 0;
        const supplierMonths = Math.floor(supplierDays / 30);
        const supplierRemainingDays = supplierDays % 30;
        const supplierWeeks = Math.floor(supplierRemainingDays / 7);
        const supplierFinalDays = supplierRemainingDays % 7;
        
        // 更新按钮组HTML
        const buttonHtml = `
            <div class="col-md-2">
                <div class="btn-group w-100">
                    <button type="button" class="btn btn-outline-primary" onclick="saveSupplier(this)" title="保存供应商">
                        <i class="bi bi-save"></i> 保存
                    </button>
                    <button type="button" class="btn btn-outline-danger" onclick="removeSupplierQuote(this)">
                        <i class="bi bi-trash"></i> 删除
                    </button>
                </div>
            </div>
        `;

        quoteElement.innerHTML = `
            <div class="row mb-2">
                <div class="col-md-4">
                    <input type="text" class="form-control supplier-name" placeholder="供应商名称" required value="${quote.supplier_name}">
                </div>
                <div class="col-md-4">
                    <input type="text" class="form-control supplier-contact" placeholder="联系方式" value="${quote.supplier_contact || ''}">
                </div>
                <div class="col-md-4">
                    <div class="input-group">
                        <span class="input-group-text">¥</span>
                        <input type="number" class="form-control supplier-price" step="0.01" min="0" required value="${quote.price}">
                    </div>
                </div>
            </div>
            <div class="row align-items-end">
                <div class="col-md-10">
                    <label class="form-label">供应商交货周期</label>
                    <div class="row g-2">
                        <div class="col-md-3">
                            <div class="input-group">
                                <input type="number" class="form-control supplier-delivery-days" min="0" value="${supplierFinalDays}" onchange="updateSupplierDeliveryDays(this)">
                                <span class="input-group-text">天</span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="input-group">
                                <input type="number" class="form-control supplier-delivery-weeks" min="0" value="${supplierWeeks}" onchange="updateSupplierDeliveryDays(this)">
                                <span class="input-group-text">周</span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="input-group">
                                <input type="number" class="form-control supplier-delivery-months" min="0" value="${supplierMonths}" onchange="updateSupplierDeliveryDays(this)">
                                <span class="input-group-text">月</span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-text mb-2">总计: <span class="supplier-total-days">${supplierDays}</span> 天</div>
                        </div>
                    </div>
                    <input type="hidden" class="supplier-expected-delivery-days" name="supplierExpectedDeliveryDays" value="${supplierDays}">
                </div>
                ${buttonHtml}
            </div>
        `;
        supplierQuotes.appendChild(quoteElement);
    });
}

// 为搜索输入添加实时搜索
document.getElementById('searchProduct').addEventListener('input', function() {
    clearTimeout(searchTimeout);
    if (this.value.trim().length > 0) {
        searchTimeout = setTimeout(searchProducts, 300);
    } else {
        document.getElementById('searchResults').innerHTML = '';
    }
});

// 点击页面其他地方时隐藏搜索结果
document.addEventListener('click', function(event) {
    const searchContainer = document.getElementById('searchProduct').closest('.search-container');
    const resultsContainer = document.getElementById('searchResults');
    
    if (!searchContainer.contains(event.target)) {
        resultsContainer.innerHTML = '';
        searchContainer.classList.remove('show-results');
    }
});

// 表单提交处理
async function submitInquiry(event) {
    console.log('Starting submitInquiry function');  // Debug log
    event.preventDefault();
    
    const submitButton = document.querySelector('button[type="submit"]');
    console.log('Submit button found:', submitButton);  // Debug log
    const originalText = submitButton.innerHTML;
    
    // 添加加载状态
    submitButton.disabled = true;
    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>提交中...';
    console.log('Submit button disabled and showing loading state');  // Debug log
    
    try {
        // 获取所有产品项
        const productItems = document.querySelectorAll('.product-item');
        console.log('Found product items:', productItems.length);  // Debug log
        
        const customer_id = document.getElementById('customer').value;
        const inquiry_date = document.getElementById('inquiryDate').value;
        console.log('Customer ID:', customer_id, 'Inquiry date:', inquiry_date);  // Debug log

        // 验证客户选择
        if (!customer_id) {
            console.log('No customer selected');  // Debug log
            showToast('warning', '请选择客户');
            return;
        }

        // 遍历处理每个产品
        for (const productItem of productItems) {
            // 收集产品数据
            const formData = {
                customer_id: customer_id,
                product_id: productItem.dataset.productId,
                brand: productItem.querySelector('.product-brand').value.trim(),
                product_name: productItem.querySelector('.product-name').value,
                quantity: parseInt(productItem.querySelector('.product-quantity').value),
                my_price: parseFloat(productItem.querySelector('.product-price').value),
                expected_delivery_days: parseInt(productItem.querySelector('.expected-delivery-days').value),
                inquiry_date: inquiry_date,
                supplier_quotes: []
            };
            
            console.log('Processing product:', formData);  // Debug log

            // 验证数量和交货周期
            if (formData.quantity < 1) {
                console.log('Invalid quantity:', formData.quantity);  // Debug log
                showToast('warning', '请输入有效的数量');
                return;
            }

            if (formData.expected_delivery_days < 1) {
                console.log('Invalid delivery days:', formData.expected_delivery_days);  // Debug log
                showToast('warning', '请输入预计交货周期');
                return;
            }

            // 收集供应商报价
            const supplierQuotes = productItem.querySelectorAll('.supplier-quote');
            console.log('Found supplier quotes:', supplierQuotes.length);  // Debug log
            
            for (const quote of supplierQuotes) {
                const supplierName = quote.querySelector('.supplier-name').value.trim();
                const contactInfo = quote.querySelector('.supplier-contact').value.trim();
                const price = quote.querySelector('.supplier-price').value;
                const deliveryDays = parseInt(quote.querySelector('.supplier-expected-delivery-days').value) || 0;
                
                if (!supplierName || !price) {
                    console.log('Missing supplier info - Name:', supplierName, 'Price:', price);  // Debug log
                    showToast('warning', '请填写完整的供应商报价信息');
                    return;
                }
                
                formData.supplier_quotes.push({
                    supplier_name: supplierName,
                    contact_info: contactInfo,
                    price: parseFloat(price),
                    delivery_days: deliveryDays
                });
            }

            // 提交当前产品的数据
            console.log('Submitting product data:', formData);  // Debug log
            const response = await fetch('/inquiries', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });
            
            if (!response.ok) {
                throw new Error('创建咨询记录失败');
            }
            
            console.log('Product submitted successfully');  // Debug log
        }
        
        showToast('success', '提交成功！');
        
        // 延迟1秒后刷新页面，让用户能看到成功提示
        setTimeout(() => {
            window.location.reload();
        }, 1000);
        
    } catch (error) {
        console.error('提交失败:', error);
        showToast('error', '提交失败，请重试');
    } finally {
        submitButton.disabled = false;
        submitButton.innerHTML = originalText;
        console.log('Submit button restored to original state');  // Debug log
    }
}

// 搜索产品
async function searchProducts() {
    const searchInput = document.getElementById('searchProduct');
    const searchResults = document.getElementById('searchResults');
    const searchContainer = searchInput.closest('.search-container');
    const query = searchInput.value.trim();
    const customer_id = document.getElementById('customer').value;
    
    if (!query) {
        searchResults.innerHTML = '';
        searchContainer.classList.remove('show-results');
        return;
    }

    try {
        const response = await fetch(`/products/search?query=${encodeURIComponent(query)}${customer_id ? '&customer_id=' + customer_id : ''}`);
        if (!response.ok) {
            throw new Error('搜索失败');
        }
        
        const products = await response.json();
        
        // 显示搜索结果容器
        searchContainer.classList.add('show-results');
        
        if (products.length === 0) {
            searchResults.innerHTML = '<div class="p-2 text-muted">未找到相关产品</div>';
            return;
        }

        searchResults.innerHTML = products.map(product => `
            <div class="search-result-item p-2" onclick="selectProduct(${JSON.stringify(product).replace(/"/g, '&quot;')})">
                <div class="d-flex justify-content-between">
                    <div>
                        <strong>${product.product_name}</strong>
                        ${product.brand ? `<span class="text-muted ms-2">${product.brand}</span>` : ''}
                    </div>
                    <div class="text-end">
                        <span class="badge bg-primary">¥${product.my_price}</span>
                    </div>
                </div>
                <div class="small text-muted">
                    ${product.customer_name} · ${new Date(product.inquiry_date).toLocaleDateString()}
                </div>
                </div>
            `).join('');
            
    } catch (error) {
        console.error('搜索产品失败:', error);
        const now = Date.now();
        if (!lastError || now - lastErrorTime > 5000) {
            showToast('error', '搜索失败，请重试');
            lastError = error;
            lastErrorTime = now;
        }
        // 显示错误信息在搜索结果区域
        searchResults.innerHTML = '<div class="p-2 text-danger">搜索失败，请重试</div>';
        searchContainer.classList.add('show-results');
    }
}

// 选择产品
async function selectProduct(product) {
    // 如果有客户信息，自动选择客户
    if (product.customer_id) {
        const customerSelect = document.getElementById('customer');
        if (customerSelect) {
            customerSelect.value = product.customer_id;
            // 触发change事件以更新任何依赖的逻辑
            customerSelect.dispatchEvent(new Event('change'));
        }
    }
    
    // 填充产品信息
    fillProductInfo(product);
    
    // 清空搜索
    const searchInput = document.getElementById('searchProduct');
    const searchResults = document.getElementById('searchResults');
    searchInput.value = '';
    searchResults.innerHTML = '';
    searchInput.closest('.search-container').classList.remove('show-results');
    
    // 加载该产品的供应商历史报价
    try {
        const response = await fetch(`/suppliers/product-history?product_name=${encodeURIComponent(product.product_name)}`);
        if (!response.ok) {
            throw new Error('获取供应商历史报价失败');
        }
        
        const result = await response.json();
        if (result.supplier_quotes && result.supplier_quotes.length > 0) {
            // 找到产品项容器
            const productItem = document.querySelector('.product-item');
            
            // 清空现有供应商报价
            const supplierQuotes = productItem.querySelector('.supplier-quotes');
            const supplierQuotesTitle = supplierQuotes.querySelector('h6');
            supplierQuotes.innerHTML = '';
            supplierQuotes.appendChild(supplierQuotesTitle);
            
            // 添加每个供应商的最新报价
            result.supplier_quotes.forEach(quote => {
                addSupplierQuoteWithData(supplierQuotes, quote);
            });
            
            // 更新供应商报价行模板
            updateSupplierQuoteTemplate();
        }
    } catch (error) {
        console.error('获取供应商历史报价失败:', error);
    }
}

// 添加新产品
function addProduct() {
    const productList = document.getElementById('productList');
    const template = productList.querySelector('.product-item').cloneNode(true);
    
    // 清空所有输入值
    template.querySelectorAll('input').forEach(input => {
        input.value = '';
    });
    
    // 重置交货周期
    const deliveryInputs = template.querySelectorAll('.delivery-months, .delivery-weeks, .delivery-days');
    deliveryInputs.forEach(input => {
        input.value = '0';
    });
    template.querySelector('.total-days').textContent = '0';
    template.querySelector('.expected-delivery-days').value = '0';
    
    // 只保留一个供应商报价行
    const supplierQuotes = template.querySelector('.supplier-quotes');
    const firstQuote = supplierQuotes.querySelector('.supplier-quote').cloneNode(true);
    firstQuote.querySelectorAll('input').forEach(input => {
        input.value = '';
    });
    supplierQuotes.innerHTML = '<h6>供应商报价</h6>';
    supplierQuotes.appendChild(firstQuote);
    
    productList.appendChild(template);
    
    // 为新添加的产品名称输入框设置监听器
    setupProductNameListener(template);
    // 更新供应商报价行模板
    updateSupplierQuoteTemplate();
}

// 删除产品
function removeProduct(button) {
    const productItems = document.querySelectorAll('.product-item');
    if (productItems.length > 1) {
        button.closest('.product-item').remove();
    } else {
        alert('至少需要保留一个产品');
    }
}

// 设置产品名称输入监听器
function setupProductNameListener(container = document) {
    console.log('Setting up product name listeners');
    const productNameInputs = container.querySelectorAll('.product-name');
    console.log(`Found ${productNameInputs.length} product name inputs`);
    
    productNameInputs.forEach(input => {
        // 如果已经设置过监听器，就跳过
        if (input.dataset.hasListener) {
            return;
        }
        
        console.log('Setting up listener for input:', input);
        
        // 添加监听器
        input.addEventListener('input', function(event) {
            console.log('Product name input event triggered');
            clearTimeout(this.searchTimeout);
            this.searchTimeout = setTimeout(() => {
                const productName = this.value.trim();
                console.log(`Searching for product: ${productName}`);
                if (productName) {
                    loadProductSupplierHistory(productName, this);
                }
            }, 500);
        });
        
        input.addEventListener('change', function(event) {
            console.log('Product name change event triggered');
            const productName = this.value.trim();
            if (productName) {
                loadProductSupplierHistory(productName, this);
            }
        });
        
        // 标记已设置监听器
        input.dataset.hasListener = 'true';
        
        // 如果输入框已有值，立即触发加载
        if (input.value.trim()) {
            console.log('Input has initial value, loading history:', input.value);
            loadProductSupplierHistory(input.value.trim(), input);
        }
    });
}

// 加载产品的供应商历史报价
async function loadProductSupplierHistory(productName, input) {
    try {
        console.log('Fetching supplier history for product:', productName);
        const response = await fetch(`/suppliers/product-history?product_name=${encodeURIComponent(productName)}`);
        if (!response.ok) {
            throw new Error('获取供应商历史报价失败');
        }
        
        const result = await response.json();
        console.log('Received supplier history:', result);
        
        // 找到产品项容器
        const productItem = input.closest('.product-item');
        if (!productItem) {
            console.error('Could not find product item container');
            return;
        }
        
        // 找到供应商报价容器
        const supplierQuotes = productItem.querySelector('.supplier-quotes');
        if (!supplierQuotes) {
            console.error('Could not find supplier quotes container');
            return;
        }
        
        // 保存标题元素
        const supplierQuotesTitle = supplierQuotes.querySelector('h6');
        
        // 清空现有供应商报价
        supplierQuotes.innerHTML = '';
        supplierQuotes.appendChild(supplierQuotesTitle);
        
        if (result.supplier_quotes && result.supplier_quotes.length > 0) {
            // 添加每个供应商的报价
            result.supplier_quotes.forEach(quote => {
                console.log('Adding supplier quote:', quote);
                addSupplierQuoteWithData(supplierQuotes, quote);
            });
            
            // 更新供应商报价行模板
            updateSupplierQuoteTemplate();
            
            // 显示成功提示
            showToast('success', `已加载 ${result.supplier_quotes.length} 个供应商报价`);
        } else {
            console.log('No supplier quotes found for product:', productName);
            // 添加一个空的供应商报价行
            const emptyQuote = document.createElement('div');
            emptyQuote.className = 'supplier-quote mb-3';
            emptyQuote.innerHTML = `
                <div class="row mb-2">
                    <div class="col-md-4">
                        <input type="text" class="form-control supplier-name" placeholder="供应商名称" required>
                    </div>
                    <div class="col-md-4">
                        <input type="text" class="form-control supplier-contact" placeholder="联系方式">
                    </div>
                    <div class="col-md-4">
                        <div class="input-group">
                            <span class="input-group-text">¥</span>
                            <input type="number" class="form-control supplier-price" step="0.01" min="0" required>
                        </div>
                    </div>
                </div>
                <div class="row align-items-end">
                    <div class="col-md-10">
                        <label class="form-label">供应商交货周期</label>
                        <div class="row g-2">
                            <div class="col-md-3">
                                <div class="input-group">
                                    <input type="number" class="form-control supplier-delivery-days" min="0" value="0" onchange="updateSupplierDeliveryDays(this)">
                                    <span class="input-group-text">天</span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="input-group">
                                    <input type="number" class="form-control supplier-delivery-weeks" min="0" value="0" onchange="updateSupplierDeliveryDays(this)">
                                    <span class="input-group-text">周</span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="input-group">
                                    <input type="number" class="form-control supplier-delivery-months" min="0" value="0" onchange="updateSupplierDeliveryDays(this)">
                                    <span class="input-group-text">月</span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-text mb-2">总计: <span class="supplier-total-days">0</span> 天</div>
                            </div>
                        </div>
                        <input type="hidden" class="supplier-expected-delivery-days" name="supplierExpectedDeliveryDays" value="0">
                    </div>
                    <div class="col-md-2">
                        <div class="btn-group w-100">
                            <button type="button" class="btn btn-outline-primary" onclick="saveSupplier(this)" title="保存供应商">
                                <i class="bi bi-save"></i> 保存
                            </button>
                            <button type="button" class="btn btn-outline-danger" onclick="removeSupplierQuote(this)">
                                <i class="bi bi-trash"></i> 删除
                            </button>
                        </div>
                    </div>
                </div>
            `;
            supplierQuotes.appendChild(emptyQuote);
            updateSupplierQuoteTemplate();
        }
    } catch (error) {
        console.error('获取供应商历史报价失败:', error);
        showToast('error', '获取供应商历史报价失败');
    }
}

// 添加带数据的供应商报价行
function addSupplierQuoteWithData(container, quote) {
    console.log('Adding supplier quote with data:', quote);  // Debug log
    
    // 创建新的供应商报价行
    const quoteElement = document.createElement('div');
    quoteElement.className = 'supplier-quote mb-3';
    
    // 计算供应商交货周期
    const supplierDays = quote.delivery_days || 0;
    const supplierMonths = Math.floor(supplierDays / 30);
    const supplierRemainingDays = supplierDays % 30;
    const supplierWeeks = Math.floor(supplierRemainingDays / 7);
    const supplierFinalDays = supplierRemainingDays % 7;
    
    quoteElement.innerHTML = `
        <div class="row mb-2">
            <div class="col-md-4">
                <input type="text" class="form-control supplier-name" placeholder="供应商名称" required value="${quote.supplier_name}">
            </div>
            <div class="col-md-4">
                <input type="text" class="form-control supplier-contact" placeholder="联系方式" value="${quote.supplier_contact || ''}">
            </div>
            <div class="col-md-4">
                <div class="input-group">
                    <span class="input-group-text">¥</span>
                    <input type="number" class="form-control supplier-price" step="0.01" min="0" required value="${quote.price}">
                </div>
            </div>
        </div>
        <div class="row align-items-end">
            <div class="col-md-10">
                <label class="form-label">供应商交货周期</label>
                <div class="row g-2">
                    <div class="col-md-3">
                        <div class="input-group">
                            <input type="number" class="form-control supplier-delivery-days" min="0" value="${supplierFinalDays}" onchange="updateSupplierDeliveryDays(this)">
                            <span class="input-group-text">天</span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="input-group">
                            <input type="number" class="form-control supplier-delivery-weeks" min="0" value="${supplierWeeks}" onchange="updateSupplierDeliveryDays(this)">
                            <span class="input-group-text">周</span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="input-group">
                            <input type="number" class="form-control supplier-delivery-months" min="0" value="${supplierMonths}" onchange="updateSupplierDeliveryDays(this)">
                            <span class="input-group-text">月</span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-text mb-2">总计: <span class="supplier-total-days">${supplierDays}</span> 天</div>
                    </div>
                </div>
                <input type="hidden" class="supplier-expected-delivery-days" name="supplierExpectedDeliveryDays" value="${supplierDays}">
            </div>
            <div class="col-md-2">
                <div class="btn-group w-100">
                    <button type="button" class="btn btn-outline-primary" onclick="saveSupplier(this)" title="保存供应商">
                        <i class="bi bi-save"></i> 保存
                    </button>
                    <button type="button" class="btn btn-outline-danger" onclick="removeSupplierQuote(this)">
                        <i class="bi bi-trash"></i> 删除
                    </button>
                </div>
            </div>
        </div>
    `;
    
    container.appendChild(quoteElement);
    console.log('Supplier quote element added to container');  // Debug log
}

// 提交订单
async function submitOrder() {
    const quantity = parseInt(document.getElementById('orderQuantity').value);
    const notes = document.getElementById('orderNotes').value.trim();
    
    if (isNaN(quantity) || quantity < 1) {
        showToast('error', '请输入有效的数量（≥1）');
        return;
    }

    const orderData = {
        supplier_quote_id: currentOrderData.supplier_quote_id,
        quantity: quantity,
        notes: notes
    };

    try {
        const response = await fetch('/order/create', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(orderData)
        });
        
        const result = await response.json();
        
        if (!response.ok) {
            throw new Error(result.error || '创建订单失败');
        }
        
        // 关闭订单对话框
        const modal = bootstrap.Modal.getInstance(document.getElementById('orderModal'));
        modal.hide();
        
        // 显示成功消息
        showToast('success', '订单创建成功');
        
        // 刷新询价列表
        await loadInquiries();
        
    } catch (error) {
        console.error('创建订单失败:', error);
        showToast('error', error.message || '创建订单失败，请重试');
    }
}

// 显示订单对话框
function showOrderDialog(inquiryId, quoteId, supplierName, price) {
    currentOrderData = {
        inquiry_id: inquiryId,
        supplier_quote_id: quoteId
    };
    
    // 设置对话框内容
    document.getElementById('selectedSupplier').textContent = supplierName;
    document.getElementById('selectedPrice').textContent = `¥${price.toFixed(2)}`;
    document.getElementById('orderQuantity').value = '1';  // 默认数量为1
    document.getElementById('orderNotes').value = '';
    
    // 显示对话框
    const modal = new bootstrap.Modal(document.getElementById('orderModal'));
    modal.show();
}

// 显示批量添加产品模态框
function showBulkProductModal() {
    console.log('Opening bulk product modal');
    // 检查是否选择了客户
    const customerId = document.getElementById('customer').value;
    if (!customerId) {
        showToast('warning', '请先选择客户');
        return;
    }
    
    try {
        // 获取模态框元素
        const modal = document.getElementById('bulkProductModal');
        if (!modal) {
            console.error('Bulk product modal not found');
            return;
        }
        
        // 获取表单元素
        const form = document.getElementById('bulkProductForm');
        if (!form) {
            console.error('Bulk product form not found');
            return;
        }
        
        // 重置表单和表格
        form.reset();
        const tbody = document.querySelector('#bulkProductTable tbody');
        if (tbody) {
            tbody.innerHTML = `
                <tr>
                    <td><input type="text" class="form-control form-control-sm bulk-brand" name="brand[]"></td>
                    <td><input type="text" class="form-control form-control-sm bulk-product-name" name="productName[]" required style="font-size: 1.1em"></td>
                    <td><input type="number" class="form-control form-control-sm bulk-quantity" name="quantity[]" min="1" required style="width: 80px"></td>
                    <td>
                        <div class="input-group input-group-sm">
                            <span class="input-group-text">¥</span>
                            <input type="number" class="form-control bulk-price" name="price[]" step="0.01" min="0" required>
                        </div>
                    </td>
                    <td>
                        <div class="input-group input-group-sm">
                            <input type="number" class="form-control bulk-delivery-days" name="deliveryDays[]" min="0" required>
                            <span class="input-group-text">天</span>
                        </div>
                    </td>
                    <td>
                        <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeBulkRow(this)">
                            <i class="bi bi-trash"></i>
                        </button>
                    </td>
                </tr>
            `;
        }
        
        // 显示模态框
        const modalInstance = new bootstrap.Modal(modal);
        modalInstance.show();
        
    } catch (error) {
        console.error('Error showing bulk product modal:', error);
        showToast('error', '打开批量添加窗口失败');
    }
}

// 保存批量添加的产品
async function saveBulkProducts(event) {
    event.preventDefault();
    
    console.log('Saving bulk products to main form');
    
    try {
        // Get the supplier information
        const supplierName = document.getElementById('bulkSupplierName').value.trim();
        const supplierContact = document.getElementById('bulkSupplierContact').value.trim();
        
        if (!supplierName) {
            showToast('warning', '请输入供应商名称');
            return;
        }
        
        // Get all rows from the bulk product table
        const tbody = document.querySelector('#bulkProductTable tbody');
        const rows = tbody.querySelectorAll('tr');
        
        // Validate that we have at least one product
        if (rows.length === 0) {
            showToast('warning', '请至少添加一个产品');
            return;
        }
        
        // Process each row
        for (const row of rows) {
            const brand = row.querySelector('.bulk-brand').value.trim();
            const productName = row.querySelector('.bulk-product-name').value.trim();
            const quantity = row.querySelector('.bulk-quantity').value;
            const price = row.querySelector('.bulk-price').value;
            const deliveryDays = row.querySelector('.bulk-delivery-days').value;
            
            if (!productName || !quantity || !price || !deliveryDays) {
                showToast('warning', '请填写所有必填字段');
                return;
            }
            
            // Add a new product to the main form if needed
            let productContainer = document.querySelector('.product-item:last-child');
            const isLastProductEmpty = !productContainer.querySelector('.product-name').value;
            
            if (!isLastProductEmpty) {
                addProduct();
                productContainer = document.querySelector('.product-item:last-child');
            }
            
            // Fill in the product details
            productContainer.querySelector('.product-brand').value = brand;
            productContainer.querySelector('.product-name').value = productName;
            productContainer.querySelector('.product-quantity').value = quantity;
            
            // Set delivery days
            const daysInput = productContainer.querySelector('.delivery-days');
            daysInput.value = deliveryDays;
            updateExpectedDeliveryDays(daysInput);
            
            // Add supplier quote
            const supplierQuoteContainer = productContainer.querySelector('.supplier-quotes');
            const quoteTemplate = supplierQuoteContainer.querySelector('.supplier-quote:last-child');
            
            // Fill in supplier information
            quoteTemplate.querySelector('.supplier-name').value = supplierName;
            quoteTemplate.querySelector('.supplier-contact').value = supplierContact;
            quoteTemplate.querySelector('.supplier-price').value = price;
            
            // Set supplier delivery days
            const supplierDaysInput = quoteTemplate.querySelector('.supplier-delivery-days');
            supplierDaysInput.value = deliveryDays;
            updateSupplierDeliveryDays(supplierDaysInput);
        }
        
        // Close the modal and reset the form
        const modal = bootstrap.Modal.getInstance(document.getElementById('bulkProductModal'));
        modal.hide();
        document.getElementById('bulkProductForm').reset();
        
        // Clear the table except for one empty row
        const tableBody = document.querySelector('#bulkProductTable tbody');
        tableBody.innerHTML = '';
        addBulkRow();
        
        showToast('success', '产品已添加到询价表单');
        
    } catch (error) {
        console.error('Error adding bulk products:', error);
        showToast('error', '添加产品失败');
    }
}

// 添加批量输入行
function addBulkRow() {
    const tbody = document.querySelector('#bulkProductTable tbody');
    const template = `
        <tr>
            <td><input type="text" class="form-control form-control-sm bulk-brand" name="brand[]"></td>
            <td><input type="text" class="form-control form-control-sm bulk-product-name" name="productName[]" required style="font-size: 1.1em"></td>
            <td><input type="number" class="form-control form-control-sm bulk-quantity" name="quantity[]" min="1" required style="width: 80px"></td>
            <td>
                <div class="input-group input-group-sm">
                    <span class="input-group-text">¥</span>
                    <input type="number" class="form-control bulk-price" name="price[]" step="0.01" min="0" required>
                </div>
            </td>
            <td>
                <div class="input-group input-group-sm">
                    <input type="number" class="form-control bulk-delivery-days" name="deliveryDays[]" min="0" required>
                    <span class="input-group-text">天</span>
                </div>
            </td>
            <td>
                <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeBulkRow(this)">
                    <i class="bi bi-trash"></i>
                </button>
            </td>
        </tr>
    `;
    tbody.insertAdjacentHTML('beforeend', template);
}

// 删除批量输入行
function removeBulkRow(button) {
    const tbody = document.querySelector('#bulkProductTable tbody');
    if (tbody.querySelectorAll('tr').length > 1) {
        button.closest('tr').remove();
    } else {
        showToast('warning', '至少需要保留一行');
    }
} 