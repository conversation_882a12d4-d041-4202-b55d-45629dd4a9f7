# models.py
from flask_sqlalchemy import SQLAlchemy
from datetime import datetime, timedelta
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash

db = SQLAlchemy()

class Customer(db.Model):
    __tablename__ = 'customer'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, index=True)
    address = db.Column(db.String(200), nullable=False)  # 客户地址
    phone = db.Column(db.String(20), nullable=False)     # 客户电话
    contact = db.Column(db.String(100), nullable=False)  # 联系人
    country = db.Column(db.String(50), nullable=False)   # 客户国家
    inquiries = db.relationship('ProductInquiry', backref='customer', lazy=True)
    orders = db.relationship('Order', backref='customer', lazy=True)

    def __repr__(self):
        return f"<Customer(name={self.name})>"

class ProductInquiry(db.Model):
    __tablename__ = 'product_inquiry'
    id = db.Column(db.Integer, primary_key=True)
    customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'), nullable=False)
    brand = db.Column(db.String(100))
    product_name = db.Column(db.String(100), nullable=False, index=True)
    quantity = db.Column(db.Integer, nullable=False)
    expected_delivery_days = db.Column(db.Integer, nullable=False)
    my_price = db.Column(db.Float, nullable=False)
    inquiry_date = db.Column(db.DateTime, nullable=False, default=datetime.now)
    supplier_quotes = db.relationship('SupplierQuote', back_populates='inquiry', lazy=True)
    orders = db.relationship('Order', backref='inquiry', lazy=True)

    def __repr__(self):
        return f"<ProductInquiry(product={self.product_name})>"

class Supplier(db.Model):
    __tablename__ = 'supplier'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, index=True)
    contact = db.Column(db.String(100))
    default_delivery_days = db.Column(db.Integer, default=0)
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    quotes = db.relationship('SupplierQuote', back_populates='supplier', lazy=True)

    def __repr__(self):
        return f"<Supplier(name={self.name})>"

class SupplierQuote(db.Model):
    __tablename__ = 'supplier_quote'
    id = db.Column(db.Integer, primary_key=True)
    inquiry_id = db.Column(db.Integer, db.ForeignKey('product_inquiry.id'), nullable=False)
    supplier_id = db.Column(db.Integer, db.ForeignKey('supplier.id'), nullable=False)
    price = db.Column(db.Float, nullable=False)
    my_price = db.Column(db.Float, nullable=False)
    delivery_days = db.Column(db.Integer, nullable=False)
    quantity = db.Column(db.Integer, nullable=False)
    product_name = db.Column(db.String(100), nullable=False)
    brand = db.Column(db.String(100))
    inquiry_date = db.Column(db.DateTime, nullable=False, default=datetime.now)
    
    # 关联关系
    inquiry = db.relationship('ProductInquiry', back_populates='supplier_quotes', lazy=True)
    supplier = db.relationship('Supplier', back_populates='quotes', lazy=True)
    orders = db.relationship('Order', backref='supplier_quote', lazy=True)

    def __init__(self, **kwargs):
        inquiry = kwargs.get('inquiry')
        if inquiry:
            kwargs.setdefault('product_name', inquiry.product_name)
            kwargs.setdefault('brand', inquiry.brand)
            kwargs.setdefault('quantity', inquiry.quantity)
            kwargs.setdefault('my_price', inquiry.my_price)
        super(SupplierQuote, self).__init__(**kwargs)

    def __repr__(self):
        return f"<SupplierQuote(supplier={self.supplier.name}, product={self.product_name}, price={self.price})>"

class Order(db.Model):
    __tablename__ = 'order'
    id = db.Column(db.Integer, primary_key=True)
    customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'), nullable=False)
    inquiry_id = db.Column(db.Integer, db.ForeignKey('product_inquiry.id'), nullable=False)
    supplier_quote_id = db.Column(db.Integer, db.ForeignKey('supplier_quote.id'), nullable=False)
    order_date = db.Column(db.DateTime, nullable=False, default=datetime.now)
    status = db.Column(db.String(20), nullable=False, default='pending', index=True)
    notes = db.Column(db.Text)
    
    product_name = db.Column(db.String(100), nullable=False)
    brand = db.Column(db.String(100))
    quantity = db.Column(db.Integer, nullable=False)
    price = db.Column(db.Float, nullable=False)
    my_price = db.Column(db.Float, nullable=False)
    supplier_name = db.Column(db.String(100), nullable=False)
    delivery_days = db.Column(db.Integer, nullable=False)
    expected_delivery_date = db.Column(db.DateTime)
    
    def __init__(self, **kwargs):
        supplier_quote = kwargs.get('supplier_quote')
        if supplier_quote:
            # Copy fields from supplier_quote
            kwargs.setdefault('product_name', supplier_quote.product_name)
            kwargs.setdefault('brand', supplier_quote.brand)
            kwargs.setdefault('quantity', supplier_quote.quantity)
            kwargs.setdefault('price', supplier_quote.price)
            kwargs.setdefault('my_price', supplier_quote.my_price)
            kwargs.setdefault('supplier_name', supplier_quote.supplier.name)
            kwargs.setdefault('delivery_days', supplier_quote.delivery_days)
            # Copy customer_id from inquiry
            kwargs.setdefault('customer_id', supplier_quote.inquiry.customer_id)
            
        super(Order, self).__init__(**kwargs)
        
        # Set expected_delivery_date based on order_date and delivery_days using local time
        if self.delivery_days is not None and self.order_date is not None:
            self.expected_delivery_date = self.order_date + timedelta(days=self.delivery_days)

class Inventory(db.Model):
    __tablename__ = 'inventory'
    id = db.Column(db.Integer, primary_key=True)
    order_id = db.Column(db.Integer, db.ForeignKey('order.id'), nullable=False)
    customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'), nullable=False)
    product_name = db.Column(db.String(200), nullable=False)
    quantity = db.Column(db.Integer, nullable=False)
    arrival_date = db.Column(db.DateTime)
    shipping_method = db.Column(db.String(50))  # 'sea' or 'air'
    shipping_date = db.Column(db.DateTime)
    supplier_name = db.Column(db.String(200))
    serial_number = db.Column(db.String(200))
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    # Relationships
    order = db.relationship('Order', backref=db.backref('inventory', lazy=True))
    customer = db.relationship('Customer', backref=db.backref('inventory', lazy=True))

    def __init__(self, **kwargs):
        super(Inventory, self).__init__(**kwargs)
        
    def serialize(self):
        """Convert model instance to dictionary for JSON serialization."""
        return {
            'id': self.id,
            'order_date': self.order.order_date if self.order else None,
            'product_name': self.product_name,
            'quantity': self.quantity,
            'shipping_method': self.shipping_method,
            'shipping_date': self.shipping_date,
            'supplier_name': self.supplier_name,
            'serial_number': self.serial_number
        }

class User(db.Model, UserMixin):
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    password_hash = db.Column(db.String(128))
    is_admin = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.now)
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password, method='pbkdf2:sha256')
        
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def __repr__(self):
        return f'<User {self.username}>'

class UserActivityLog(db.Model):
    __tablename__ = 'user_activity_logs'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    timestamp = db.Column(db.DateTime, default=datetime.now)
    action_type = db.Column(db.String(50))  # 例如：login, create_order, update_status
    details = db.Column(db.Text)
    ip_address = db.Column(db.String(50))

    user = db.relationship('User', backref='activities')

    def __repr__(self):
        return f'<UserActivityLog {self.action_type} by {self.user.username}>'
