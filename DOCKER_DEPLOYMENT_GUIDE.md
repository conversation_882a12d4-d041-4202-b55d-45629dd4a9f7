# Docker Deployment Guide - Product Inquiry System

This guide explains how to deploy the enhanced Product Inquiry System using Docker with all new features including dynamic serial numbers, duplicate prevention, and quote management.

## 🚀 Quick Start

### Prerequisites
- Docker 20.10+ and Docker Compose 2.0+
- At least 1GB RAM and 2GB disk space
- Port 5123 available on your host system

### 1. Environment Setup
```bash
# Copy environment template
cp .env.example .env

# Edit environment variables (especially SECRET_KEY)
nano .env
```

### 2. Build and Deploy
```bash
# Build and start the application
docker-compose up -d

# Check container status
docker-compose ps

# View logs
docker-compose logs -f web
```

### 3. Access the Application
- **Web Interface**: http://localhost:5123
- **Health Check**: http://localhost:5123/health

## 🏗️ Architecture Overview

### Multi-Stage Docker Build
The Dockerfile uses a multi-stage build approach:

1. **Builder Stage**: Compiles dependencies and creates virtual environment
2. **Production Stage**: Minimal runtime image with only necessary components

### Key Features
- ✅ **Database Auto-Initialization**: Automatically creates tables including new SerialNumber model
- ✅ **Health Checks**: Built-in database and web server health monitoring
- ✅ **Security**: Non-root user execution with proper file permissions
- ✅ **Persistence**: Named volumes for data, backups, and logs
- ✅ **Resource Limits**: Configurable CPU and memory constraints
- ✅ **Logging**: Structured logging with rotation

## 📊 New Features Included

### 1. Dynamic Serial Number Fields
- Automatically generates input fields based on product quantity
- Stores serial numbers in dedicated database table
- Supports bulk product registration with individual serial tracking

### 2. Duplicate Prevention
- Button state management prevents multiple submissions
- Loading indicators provide user feedback
- Proper error handling with retry capability

### 3. Quote Management Interface
- Dedicated management page at `/inquiry/manage-quotes`
- Filter and search functionality
- Safe deletion with order protection
- Audit logging for all operations

## 🔧 Configuration Options

### Environment Variables
Key variables in `.env` file:

```bash
# Security
SECRET_KEY=your-secure-key-here

# Database
DATABASE_URL=sqlite:///instance/quotation.db

# Application
BACKUP_ENABLED=true
LOG_LEVEL=INFO
```

### Volume Mounts
- `inquiry_data`: Database files and application data
- `inquiry_backups`: Automated database backups
- `inquiry_logs`: Application logs
- `inquiry_uploads`: User uploaded files

## 🔍 Health Monitoring

### Built-in Health Checks
The system includes comprehensive health monitoring:

```bash
# Check container health
docker-compose ps

# Manual health check
curl http://localhost:5123/health

# View health check logs
docker-compose logs web | grep health
```

### Health Check Response
```json
{
  "status": "healthy",
  "database": "connected",
  "timestamp": "2024-01-15T10:30:00"
}
```

## 🛠️ Development vs Production

### Production Deployment (Default)
```bash
# Production with optimized image
docker-compose up -d
```

### Development Mode
```bash
# Enable development profile with git-sync
docker-compose --profile development up -d

# Or use development override
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d
```

## 📋 Maintenance Commands

### Database Operations
```bash
# View database status
docker-compose exec web python -c "
from app import app, db
with app.app_context():
    print('Database tables:', db.engine.table_names())
"

# Manual backup
docker-compose exec web python -c "
from utils.backup_scheduler import create_backup
create_backup()
"
```

### Log Management
```bash
# View application logs
docker-compose logs -f web

# View specific log files
docker-compose exec web tail -f /app/logs/app.log

# Clear logs
docker-compose exec web sh -c "truncate -s 0 /app/logs/*.log"
```

### Container Management
```bash
# Restart application
docker-compose restart web

# Update application
docker-compose pull
docker-compose up -d --build

# Clean up
docker-compose down
docker system prune -f
```

## 🔒 Security Considerations

### Production Security Checklist
- [ ] Generate secure SECRET_KEY
- [ ] Use HTTPS in production (add reverse proxy)
- [ ] Regular database backups
- [ ] Monitor container logs
- [ ] Update base images regularly
- [ ] Implement network security groups
- [ ] Use secrets management for sensitive data

### File Permissions
The container runs as non-root user (appuser:1000) with proper file permissions:
- Application files: 755
- Data directories: 777 (for database writes)
- Configuration files: 644

## 🚨 Troubleshooting

### Common Issues

#### Database Connection Errors
```bash
# Check database file permissions
docker-compose exec web ls -la /app/instance/

# Recreate database
docker-compose exec web python /app/init_database.py
```

#### Port Already in Use
```bash
# Change port in docker-compose.yml
ports:
  - "5124:5123"  # Use different host port
```

#### Memory Issues
```bash
# Increase memory limits in docker-compose.yml
deploy:
  resources:
    limits:
      memory: 1G
```

### Log Analysis
```bash
# Check startup logs
docker-compose logs web | grep -E "(Starting|Error|Failed)"

# Monitor health checks
docker-compose logs web | grep health

# Database initialization logs
docker-compose logs web | grep -E "(Database|Migration)"
```

## 📈 Scaling and Performance

### Horizontal Scaling
```bash
# Scale web service
docker-compose up -d --scale web=3

# Use load balancer (nginx example)
# See nginx.conf.example for configuration
```

### Performance Tuning
- Adjust resource limits based on usage
- Enable database connection pooling
- Implement caching with Redis
- Use CDN for static assets

## 🔄 Backup and Recovery

### Automated Backups
Backups are automatically created and stored in `/app/database_backups/`

### Manual Backup
```bash
# Create backup
docker-compose exec web python -c "
from utils.backup_scheduler import create_backup
create_backup()
"

# Copy backup to host
docker cp $(docker-compose ps -q web):/app/database_backups/ ./backups/
```

### Recovery
```bash
# Restore from backup
docker-compose exec web cp /app/database_backups/backup_YYYYMMDD_HHMMSS.db /app/instance/quotation.db
docker-compose restart web
```

## 📞 Support

For issues or questions:
1. Check the troubleshooting section above
2. Review container logs: `docker-compose logs web`
3. Verify health status: `curl http://localhost:5123/health`
4. Check system resources: `docker stats`

## 🔄 Updates and Migrations

### Updating the Application
```bash
# Pull latest changes
git pull

# Rebuild and restart
docker-compose up -d --build

# Check for database migrations
docker-compose exec web python /app/init_database.py
```

The system is designed to handle updates gracefully with automatic database schema updates and backward compatibility.
