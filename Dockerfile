# Use Python Alpine image as base
FROM python:3.11-alpine

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    FLASK_APP=app.py \
    FLASK_ENV=production

# Create and set working directory
WORKDIR /app

# Install system dependencies
RUN apk add --no-cache \
    build-base \
    curl \
    sqlite-dev

# Create a non-root user
RUN addgroup -S appuser && adduser -S -G appuser -u 1000 appuser

# Create necessary directories with correct permissions
RUN mkdir -p /app/instance /app/database_backups && \
    chown -R appuser:appuser /app && \
    chmod -R 777 /app/instance /app/database_backups

# Copy requirements file
COPY --chown=appuser:appuser requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY --chown=appuser:appuser . .

# Ensure instance directory exists and has correct permissions
RUN mkdir -p /app/instance && \
    chown -R appuser:appuser /app/instance && \
    chmod -R 777 /app/instance

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 5123

# Command to run the application
CMD ["python", "app.py"] 