# Multi-stage build for production-ready Docker image
# Stage 1: Build dependencies
FROM python:3.11-alpine AS builder

# Set build environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install build dependencies
RUN apk add --no-cache \
    build-base \
    sqlite-dev \
    gcc \
    musl-dev \
    libffi-dev

# Create virtual environment
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Copy and install Python dependencies
COPY requirements.txt .
RUN pip install --upgrade pip && \
    pip install -r requirements.txt

# Stage 2: Production image
FROM python:3.11-alpine AS production

# Set production environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    FLASK_APP=app.py \
    FLASK_ENV=production \
    PATH="/opt/venv/bin:$PATH" \
    DATABASE_URL=sqlite:///instance/quotation.db \
    SECRET_KEY="" \
    BACKUP_ENABLED=true \
    LOG_LEVEL=INFO

# Install runtime dependencies only
RUN apk add --no-cache \
    sqlite \
    curl \
    bash \
    && rm -rf /var/cache/apk/*

# Copy virtual environment from builder stage
COPY --from=builder /opt/venv /opt/venv

# Create application user and group
RUN addgroup -S appuser && \
    adduser -S -G appuser -u 1000 appuser

# Create application directory structure
WORKDIR /app

# Create necessary directories with proper permissions
RUN mkdir -p \
    /app/instance \
    /app/database_backups \
    /app/logs \
    /app/static/uploads && \
    chown -R appuser:appuser /app && \
    chmod -R 755 /app && \
    chmod -R 777 /app/instance /app/database_backups /app/logs /app/static/uploads

# Copy application code
COPY --chown=appuser:appuser . .

# Create database initialization script
COPY --chown=appuser:appuser <<EOF /app/init_database.py
#!/usr/bin/env python3
"""
Database initialization script for Docker deployment
"""
import os
import sys
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, '/app')

from app import app, db
from models import SerialNumber
import sqlite3

def check_database_exists():
    """Check if database file exists"""
    db_path = '/app/instance/quotation.db'
    return os.path.exists(db_path)

def init_database():
    """Initialize database with all tables"""
    try:
        with app.app_context():
            print("Initializing database...")

            # Create all tables including SerialNumber
            db.create_all()

            print("Database initialization completed successfully!")
            return True

    except Exception as e:
        print(f"Database initialization failed: {str(e)}")
        return False

def check_database_connection():
    """Test database connectivity"""
    try:
        db_path = '/app/instance/quotation.db'
        conn = sqlite3.connect(db_path)
        conn.execute('SELECT 1')
        conn.close()
        print("Database connectivity check passed!")
        return True
    except Exception as e:
        print(f"Database connectivity check failed: {str(e)}")
        return False

if __name__ == "__main__":
    success = init_database() and check_database_connection()
    sys.exit(0 if success else 1)
EOF

# Create health check script
COPY --chown=appuser:appuser <<EOF /app/healthcheck.py
#!/usr/bin/env python3
"""
Health check script for Docker container
"""
import sys
import sqlite3
import requests
import time

def check_database():
    """Check database connectivity"""
    try:
        conn = sqlite3.connect('/app/instance/quotation.db')
        conn.execute('SELECT 1')
        conn.close()
        return True
    except Exception as e:
        print(f"Database health check failed: {e}")
        return False

def check_web_server():
    """Check if web server is responding"""
    try:
        response = requests.get('http://localhost:5123/health', timeout=5)
        return response.status_code == 200
    except Exception as e:
        print(f"Web server health check failed: {e}")
        return False

if __name__ == "__main__":
    # Wait a moment for services to start
    time.sleep(2)

    db_ok = check_database()
    web_ok = check_web_server()

    if db_ok and web_ok:
        print("Health check passed!")
        sys.exit(0)
    else:
        print("Health check failed!")
        sys.exit(1)
EOF

# Create startup script
COPY --chown=appuser:appuser <<EOF /app/startup.sh
#!/bin/bash
set -e

echo "Starting Product Inquiry System..."

# Initialize database if it doesn't exist
if [ ! -f "/app/instance/quotation.db" ]; then
    echo "Database not found. Initializing..."
    python /app/init_database.py
    if [ $? -ne 0 ]; then
        echo "Database initialization failed!"
        exit 1
    fi
else
    echo "Database exists. Checking for schema updates..."
    python /app/init_database.py
fi

# Ensure proper permissions
chmod 666 /app/instance/quotation.db 2>/dev/null || true

echo "Starting Flask application..."
exec python app.py
EOF

# Make scripts executable
RUN chmod +x /app/init_database.py /app/healthcheck.py /app/startup.sh

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 5123

# Add health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD python /app/healthcheck.py || exit 1

# Use startup script as entrypoint
CMD ["/app/startup.sh"]