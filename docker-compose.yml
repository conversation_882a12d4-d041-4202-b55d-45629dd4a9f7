version: '3.8'

services:
  web:
    build: .
    ports:
      - "0.0.0.0:5123:5123"
    volumes:
      - type: bind
        source: ./instance
        target: /app/instance
      - type: bind
        source: ./database_backups
        target: /app/database_backups
      - type: bind
        source: .
        target: /app
    environment:
      - FLASK_APP=app.py
      - FLASK_ENV=development
      - FLASK_DEBUG=1
      - DATABASE_URL=sqlite:///instance/quotation.db
      - SECRET_KEY=${SECRET_KEY:-default_secret_key}
      - INSTANCE_PATH=/app/instance
    user: "${UID:-1000}:${GID:-1000}"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5123/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    command: ["flask", "run", "--host=0.0.0.0", "--port=5123"]

  git-sync:
    image: alpine/git:latest
    volumes:
      - .:/git-sync
      - ~/.ssh:/root/.ssh:ro  # For SSH key authentication if needed
    working_dir: /git-sync
    entrypoint: [ "/bin/sh", "-c" ]
    command: |
      while true; do
        git pull || true;
        sleep 30;
      done
    restart: unless-stopped 