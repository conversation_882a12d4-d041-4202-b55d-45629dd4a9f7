version: '3.8'

services:
  web:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: product-inquiry-system
    ports:
      - "0.0.0.0:5123:5123"
    volumes:
      # Use named volumes for production data persistence
      - inquiry_data:/app/instance
      - inquiry_backups:/app/database_backups
      - inquiry_logs:/app/logs
      - inquiry_uploads:/app/static/uploads
      # Development mode: uncomment to bind mount source code
      # - type: bind
      #   source: .
      #   target: /app
    environment:
      # Flask Configuration
      - FLASK_APP=app.py
      - FLASK_ENV=production
      - FLASK_DEBUG=0

      # Database Configuration
      - DATABASE_URL=sqlite:///instance/quotation.db

      # Security Configuration
      - SECRET_KEY=${SECRET_KEY:-default_secret_key}

      # Application Configuration
      - BACKUP_ENABLED=true
      - LOG_LEVEL=INFO
      - INSTANCE_PATH=/app/instance

    restart: unless-stopped

    # Enhanced health check using our custom endpoint
    healthcheck:
      test: ["CMD", "python", "/app/healthcheck.py"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

    # Resource limits for production
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

    # Logging configuration
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Development git-sync service (optional)
  git-sync:
    image: alpine/git:latest
    volumes:
      - .:/git-sync
      - ~/.ssh:/root/.ssh:ro  # For SSH key authentication if needed
    working_dir: /git-sync
    entrypoint: [ "/bin/sh", "-c" ]
    command: |
      while true; do
        git pull || true;
        sleep 30;
      done
    restart: unless-stopped
    profiles:
      - development  # Only start in development profile

# Named volumes for data persistence
volumes:
  inquiry_data:
    driver: local
  inquiry_backups:
    driver: local
  inquiry_logs:
    driver: local
  inquiry_uploads:
    driver: local

# Network configuration
networks:
  default:
    name: inquiry-network